# 组织架构菜单自动展开功能

## 功能概述

为 `ost-org-menu` 组件添加了自动展开子节点的功能，可以根据配置自动展开指定层级的节点，避免用户需要手动点击多次才能查看深层数据。

## 新增参数

### autoExpandLevels

- **类型**: `number`
- **默认值**: `0`
- **说明**: 控制自动展开的层级数
  - `0`: 不自动展开（默认行为）
  - `1`: 自动展开第一层
  - `2`: 自动展开前两层
  - `3`: 自动展开前三层
  - `-1`: 展开所有层级

## 使用示例

### 基本用法

```html
<!-- 不自动展开（默认） -->
<ost-org-menu
  (itemClick)="onOrgMenu($event)"
  [showSearch]="false"
  [isDefaultSelect]="false"
  [showDirBtn]="true"
  dirBtnText="实时监控">
</ost-org-menu>

<!-- 自动展开前3层 -->
<ost-org-menu
  (itemClick)="onOrgMenu($event)"
  [showSearch]="false"
  [isDefaultSelect]="false"
  [showDirBtn]="true"
  dirBtnText="实时监控"
  [autoExpandLevels]="3">
</ost-org-menu>

<!-- 展开所有层级 -->
<ost-org-menu
  (itemClick)="onOrgMenu($event)"
  [showSearch]="false"
  [isDefaultSelect]="false"
  [showDirBtn]="true"
  dirBtnText="实时监控"
  [autoExpandLevels]="-1">
</ost-org-menu>
```

### 实际应用场景

#### 实时监控页面
在实时监控页面中，为了方便用户快速查看经理层下的数据，设置自动展开前3层：

```html
<ost-org-menu
  (itemClick)="onOrgMenu($event)"
  [showSearch]="false"
  [isDefaultSelect]="false"
  [showDirBtn]="true"
  dirBtnText="实时监控"
  [autoExpandLevels]="3">
</ost-org-menu>
```

这样用户就不需要手动点击4次才能看到经理层下的数据了。

## 技术实现

### 核心方法

```typescript
/**
 * 自动展开节点到指定层级
 * @param nodes 节点数组
 * @param currentLevel 当前层级
 */
private autoExpandNodes(nodes: OstTreeListItem[], currentLevel: number): void {
  if (!nodes || nodes.length === 0) {
    return;
  }

  for (const node of nodes) {
    // 如果设置为-1，展开所有层级；否则只展开到指定层级
    if (this.autoExpandLevels === -1 || currentLevel < this.autoExpandLevels) {
      if (node.children && node.children.length > 0) {
        node.expanded = true;
        // 递归展开子节点
        this.autoExpandNodes(node.children, currentLevel + 1);
      }
    }
  }
}
```

### 集成位置

自动展开功能在数据转换完成后执行：

```typescript
// 在 transformationOrg 和 transformation 方法中
// 自动展开指定层级的节点
if (this.autoExpandLevels > 0 || this.autoExpandLevels === -1) {
  this.autoExpandNodes(result, 0);
}
```

## 注意事项

1. **性能考虑**: 当设置 `autoExpandLevels="-1"` 时，会展开所有层级，如果数据量很大可能会影响性能
2. **用户体验**: 建议根据实际的组织架构层级深度来设置合适的展开层级数
3. **兼容性**: 该功能向后兼容，不设置 `autoExpandLevels` 或设置为 `0` 时保持原有行为

## 更新日志

- **2024-08-04**: 新增 `autoExpandLevels` 参数，支持自动展开指定层级的节点
